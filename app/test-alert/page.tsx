"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export default function TestAlertPage() {
  const [showDialog1, setShowDialog1] = useState(false);
  const [showDialog2, setShowDialog2] = useState(false);
  const [showDialog3, setShowDialog3] = useState(false);

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">
          AlertDialog 居中测试
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* 测试 1: 基本 AlertDialog */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">基本 AlertDialog</h2>
            <Button 
              onClick={() => setShowDialog1(true)}
              className="w-full"
            >
              显示基本对话框
            </Button>
          </div>

          {/* 测试 2: 长内容 AlertDialog */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">长内容 AlertDialog</h2>
            <Button 
              onClick={() => setShowDialog2(true)}
              className="w-full"
            >
              显示长内容对话框
            </Button>
          </div>

          {/* 测试 3: 自定义样式 AlertDialog */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">自定义样式 AlertDialog</h2>
            <Button 
              onClick={() => setShowDialog3(true)}
              className="w-full"
            >
              显示自定义对话框
            </Button>
          </div>
        </div>

        {/* 基本 AlertDialog */}
        <AlertDialog open={showDialog1} onOpenChange={setShowDialog1}>
          <AlertDialogContent className="max-w-md">
            <AlertDialogHeader>
              <AlertDialogTitle>基本对话框</AlertDialogTitle>
              <AlertDialogDescription>
                这是一个基本的 AlertDialog，应该显示在页面中心。
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogAction onClick={() => setShowDialog1(false)}>
                确定
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* 长内容 AlertDialog */}
        <AlertDialog open={showDialog2} onOpenChange={setShowDialog2}>
          <AlertDialogContent className="max-w-lg">
            <AlertDialogHeader>
              <AlertDialogTitle>长内容对话框</AlertDialogTitle>
              <AlertDialogDescription className="space-y-2">
                <p>这是一个包含较长内容的 AlertDialog。</p>
                <p>它应该始终显示在页面中心，无论内容有多长。</p>
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogAction onClick={() => setShowDialog2(false)}>
                确定
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* 自定义样式 AlertDialog */}
        <AlertDialog open={showDialog3} onOpenChange={setShowDialog3}>
          <AlertDialogContent className="max-w-sm bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <AlertDialogHeader className="text-center">
              <AlertDialogTitle className="text-orange-800">
                自定义样式对话框
              </AlertDialogTitle>
              <AlertDialogDescription className="text-orange-700">
                这是一个带有自定义样式的 AlertDialog，
                使用了橙色主题，应该完美居中显示。
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="flex justify-center">
              <AlertDialogAction 
                onClick={() => setShowDialog3(false)}
                className="bg-orange-500 hover:bg-orange-600 text-white"
              >
                很棒！
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* 说明文档 */}
        <div className="mt-12 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">测试说明</h2>
          <div className="space-y-3 text-gray-700">
            <p>
              <strong>测试目的：</strong>验证 AlertDialog 组件是否能够正确居中显示在页面中心。
            </p>
            <p>
              <strong>测试方法：</strong>
            </p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>点击不同的按钮打开不同类型的对话框</li>
              <li>观察对话框是否显示在页面的正中心</li>
              <li>调整浏览器窗口大小，验证对话框是否保持居中</li>
              <li>在不同设备尺寸下测试响应式效果</li>
            </ul>
            <p>
              <strong>预期结果：</strong>所有对话框都应该完美居中显示，无论内容长度或样式如何。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

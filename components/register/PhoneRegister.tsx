"use client";

import { useState, useEffect, useMemo, useRef, useCallback } from "react";
import { X, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { CountryCodeSelector } from "./country-code-select";
import { cn } from "@/lib/utils";
import { IEmailCode, IEmailPassword, IPhonepassword } from "@/server/login";
import {
  useEmailCodeSend,
  useEmailSign,
  usePhoneCodeSend,
  usePhonePasswordLogin,
} from "@/hooks/queryHook";
import { useRouter } from "next/navigation";

// 全局变量来存储验证码实例，避免组件重新渲染时丢失
let globalCaptchaInstance: any = null;

export function PerfectPhoneRegistration() {
  console.log("🔄 PerfectPhoneRegistration 组件渲染");
  const router = useRouter();
  const [countryCode, setCountryCode] = useState("86");
  const [mobileNumber, setPhoneNumber] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [signinType, setSigninType] = useState("confirmation_code");
  const [isMobileShow, setMobileShow] = useState(true);
  const [password, setPassword] = useState("");
  const [email, setEmail] = useState("");
  const [showErrorDialog, setShowErrorDialog] = useState(true);
  const [errorMessage, setErrorMessage] = useState("");
  const captchaRef = useRef<any>(null);
  const [isCaptchaReady, setIsCaptchaReady] = useState(false);
  const initializationRef = useRef(false); // 防止重复初始化
  const stateRef = useRef({} as any);
  const [captchaValidated, setCaptchaValidated] = useState(false);

  // 在每次渲染时打印当前状态
  console.log("当前 captchaValidated 状态:", captchaValidated);

  // 使用 ref 来保存最新的 setCaptchaValidated 函数
  const setCaptchaValidatedRef = useRef(setCaptchaValidated);
  setCaptchaValidatedRef.current = setCaptchaValidated;

  const [captchaData, setCaptchaData] = useState({
    captchaID: "",
    captchaOutput: "",
    genTime: "",
    lotNumber: "",
    passToken: "",
  });

  const loadGeetestScript = async () => {
    return new Promise<void>((resolve, reject) => {
      // 检查脚本是否已经加载
      if (document.getElementById("geetest-script")) {
        console.log("极验脚本已存在");
        resolve();
        return;
      }

      // 检查 initGeetest4 是否已经可用
      if (typeof (window as any).initGeetest4 === "function") {
        console.log("initGeetest4 已可用");
        resolve();
        return;
      }

      console.log("开始加载极验脚本...");
      const script = document.createElement("script");
      script.src = "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/gt4.js";
      script.id = "geetest-script";
      script.async = true;
      script.onload = () => {
        console.log("极验脚本加载成功");
        resolve();
      };
      script.onerror = (error) => {
        console.error("极验脚本加载失败:", error);
        reject(new Error("极验JS加载失败"));
      };
      document.head.appendChild(script);
    });
  };
  // 修复类型报错，给 window 增加类型声明，并为 captcha 参数添加类型
  const init = async () => {
    console.log("🔄 init 函数被调用");

    // 防止重复初始化
    if (initializationRef.current) {
      console.log("⚠️ 极验已经初始化过了，跳过重复初始化");
      return;
    }

    try {
      initializationRef.current = true;
      await loadGeetestScript();

      if (typeof (window as any)?.initGeetest4 !== "function") {
        console.error("initGeetest4 未定义，脚本可能加载失败");
        return;
      }

      // 只有在 initGeetest4 存在时才调用
      (window as any).initGeetest4(
        {
          captchaId: "dcfe08f2caf52629be6605880d3ef7fe",
          product: "bind", // 展现形式
          hideSuccess: true, // 是否隐藏验证成功弹窗
          mask: {
            outside: false, // 阻止点击蒙层关闭弹窗
            bgColor: "transparent",
          },
        },
        function (captcha: any) {
          // captcha为验证码实例

          captcha.onReady(() => {
            // captcha.showCaptcha();
            globalCaptchaInstance = captcha; // 同时保存到全局变量

            console.log("globalCaptchaInstance:", globalCaptchaInstance);
          });

          captcha.onSuccess(() => {
            try {
              const res = captcha.getValidate();
              console.log("验证码验证成功，validate:", res);

              // 不要在这里调用 handleLogin()，而是设置验证码数据
              // 让用户手动点击登录按钮，或者使用 useEffect 监听 captchaData 变化
              const newCaptchaData = {
                captchaID: res.captcha_id,
                captchaOutput: res.captcha_output,
                genTime: res.gen_time,
                lotNumber: res.lot_number,
                passToken: res.pass_token,
              };

              console.log("设置验证码数据:", newCaptchaData);
              console.log("原始响应数据:", res);

              setCaptchaData(newCaptchaData);

              console.log("准备设置 captchaValidated 为 true");
              console.log("当前 captchaValidated 值:", captchaValidated);

              // 使用 ref 中的最新函数来避免闭包问题
              console.log("使用 ref 中的最新 setCaptchaValidated 函数");
              console.log(
                "setCaptchaValidatedRef.current:",
                setCaptchaValidatedRef.current
              );

              try {
                setCaptchaValidatedRef.current(true);
                console.log(
                  "✅ 第一次调用 setCaptchaValidatedRef.current(true) 成功"
                );
              } catch (error) {
                console.error("❌ 第一次调用失败:", error);
              }

              // 也尝试函数式更新
              try {
                setCaptchaValidatedRef.current((prev) => {
                  console.log("函数式更新 captchaValidated，prev:", prev);
                  return true;
                });
                console.log("✅ 函数式更新调用成功");
              } catch (error) {
                console.error("❌ 函数式更新失败:", error);
              }

              // 作为备用方案，直接调用 handleLogin
              console.log("🔄 作为备用方案，直接调用 handleLogin");
              setTimeout(() => {
                console.log("延迟调用 handleLogin");
                // 这里需要创建一个不依赖状态的登录函数
                handleLoginDirectly();
              }, 100);

              // 直接调用登录，不依赖 useEffect
              console.log("直接调用登录函数");
              // handleLoginWithCaptchaData(newCaptchaData);
            } catch (error) {
              console.log("获取验证码数据失败:", error);
            }
          });

          captcha.onClose(() => {});
        }
      );
    } catch (error) {
      console.error("极验初始化失败:", error);
      initializationRef.current = false; // 重置标志，允许重新初始化
    }
  };

  useEffect(() => {
    init();
  }, []);

  useEffect(() => {
    console.log("🔥 useEffect 被触发 - captchaValidated:", captchaValidated);
    if (captchaValidated) {
      console.log("✅ 验证码已验证，触发登录");
      handleLogin();
      // 重置状态，避免重复触发
      setCaptchaValidated(false);
    } else {
      console.log("❌ captchaValidated 为 false，不触发登录");
    }
  }, [captchaValidated]);

  // 添加一个独立的 useEffect 来监听所有状态变化
  useEffect(() => {
    stateRef.current = {
      verificationCode,
      countryCode,
      mobileNumber,
      signinType,
      isMobileShow,
      password,
      email,
    };
    console.log({
      verificationCode,
      countryCode,
      mobileNumber,
      signinType,
      isMobileShow,
      password,
      email,
    });
  }, [
    verificationCode,
    countryCode,
    mobileNumber,
    signinType,
    isMobileShow,
    password,
    email,
  ]);

  const getLoginType = useMemo(() => {
    switch (signinType) {
      case "confirmation_code":
        if (isMobileShow) {
          return "phoneCode";
        } else {
          return "emailCode";
        }
      case "password":
        if (isMobileShow) {
          return "phonePassword";
        } else {
          return "emailPassword";
        }
    }
  }, [signinType, isMobileShow]);

  const isFormValid = useMemo(() => {
    switch (getLoginType) {
      case "phoneCode":
        return (
          codeSent &&
          mobileNumber.trim() !== "" &&
          verificationCode.trim() !== ""
        );
      case "emailCode":
        return (
          codeSent && email.trim() !== "" && verificationCode.trim() !== ""
        );
      case "phonePassword":
        return mobileNumber.trim() !== "" && password.trim() !== "";
      case "emailPassword":
        return email.trim() !== "" && password.trim() !== "";
    }
  }, [getLoginType, mobileNumber, email, password, verificationCode]);

  // 倒计时逻辑
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // 监听验证码数据变化，当验证码验证成功后自动执行登录

  const phonePasswordLogin = usePhonePasswordLogin();
  const emailSign = useEmailSign();
  const phoneCodeSend = usePhoneCodeSend();
  const emailCodeSend = useEmailCodeSend();

  const handlePhonePasswordLogin = async (data: IPhonepassword) => {
    try {
      phonePasswordLogin.mutate(data, {
        onSuccess: (response) => {
          console.log("response", response);
          console.log("登录成功！");
          localStorage.setItem("token", response.data.token.value);
          router.push("/");
          // 处理登录成功的逻辑
        },
        onError: (error: any) => {
          // 优先使用 captchaRef，如果为空则使用全局变量
          const captchaInstance = globalCaptchaInstance;

          if (captchaInstance) {
            console.log("✅ 找到验证码实例，显示验证码");
            captchaInstance.showCaptcha();
          } else {
            console.warn("❌ 极验验证码还未初始化完成，无法显示验证码");
            // 可以考虑延迟重试或者提示用户稍后再试
          }

          console.log("登录失败，请检查账号密码", error);
          const errorMsg =
            error?.response?.data?.meta?.message ||
            "登录失败，请检查手机号和密码";
          setErrorMessage(errorMsg);
          setShowErrorDialog(true);
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handleEmailSign = async (data: IEmailPassword) => {
    try {
      emailSign.mutate(data, {
        onSuccess: (response) => {
          console.log("response", response);
          console.log("登录成功！");
          localStorage.setItem("token", response.data.token.value);
          router.push("/");
          // 处理登录成功的逻辑
        },
        onError: (error: any) => {
          console.log("登录失败，请检查账号密码", error);
          const errorMsg =
            error?.response?.data?.meta?.message ||
            "登录失败，请检查邮箱和密码";
          setErrorMessage(errorMsg);
          setShowErrorDialog(true);
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handleEmailSendCode = async () => {
    if ((mobileNumber.trim() || email.trim()) && countdown === 0) {
      setIsLoading(true);
      try {
        const data = {
          email,
          mobileNumber,
          countryCode: parseInt(countryCode),
          clientId: "100033",
          category: "Text",
          codeLength: 4,
          language: "zh-CN",
        };
        // 模拟发送验证码
        emailCodeSend.mutate(data, {
          onSuccess: (response) => {
            console.log("response", response);
            setCodeSent(true);
            setCountdown(60);
          },
          onError: (error: any) => {
            console.log("error:", error);
          },
        });
      } catch (error: any) {
        console.log("发送验证码失败", error);
        const errorMsg =
          error?.response?.data?.meta?.message || "发送验证码失败，请稍后重试";
        setErrorMessage(errorMsg);
        setShowErrorDialog(true);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handlePhoneCodeSend = async () => {
    if ((mobileNumber.trim() || email.trim()) && countdown === 0) {
      setIsLoading(true);
      try {
        const data = {
          mobileNumber,
          countryCode: parseInt(countryCode),
          clientId: "100033",
          category: "Text",
          codeLength: 4,
          language: "zh-CN",
          captcha: {
            captchaID: "",
            captchaOutput: "",
            genTime: "",
            lotNumber: "",
            passToken: "",
          },
        };
        // 模拟发送验证码
        phoneCodeSend.mutate(data, {
          onSuccess: (response) => {
            console.log("response", response);
            setCodeSent(true);
            setCountdown(60);
          },
          onError: (error: any) => {
            console.log("error:", error);
          },
        });
      } catch (error: any) {
        console.log("发送验证码失败", error);
        const errorMsg =
          error?.response?.data?.meta?.message || "发送验证码失败，请稍后重试";
        setErrorMessage(errorMsg);
        setShowErrorDialog(true);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // 兼容性函数，供 useEffect 调用

  // 直接登录函数，不依赖表单验证状态
  const handleLoginDirectly = async () => {
    const {
      verificationCode,
      countryCode,
      mobileNumber,
      signinType,
      isMobileShow,
      password,
      email,
    } = stateRef.current;
    console.log(`stateRef:`, stateRef);
    console.log("🚀 handleLoginDirectly 被调用");
    console.log("当前登录类型:", getLoginType);
    console.log("当前验证码数据:", captchaData);

    setIsLoading(true);

    try {
      if (isMobileShow) {
        console.log("📱 执行手机登录");
        await handlePhonePasswordLogin({
          code: verificationCode,
          countryCode: countryCode,
          mobileNumber,
          signinType: signinType,
          captcha: captchaData,
          extra: {},
          clientId: "100033",
          password,
        });
      } else {
        console.log("📧 执行邮箱登录");
        await handleEmailSign({
          code: verificationCode,
          email,
          signinType: signinType,
          extra: {},
          clientId: "100033",
          password,
          captcha: captchaData,
        });
      }
    } catch (error) {
      console.error("登录过程中出错:", error);
    } finally {
      setIsLoading(false);
    }
  };
  // 普通登录（不需要验证码的情况）
  const handleLogin = useCallback(async () => {
    console.log("🚀 handleLogin 被调用");
    console.log("isFormValid:", isFormValid);
    console.log("getLoginType:", getLoginType);
    console.log("codeSent:", codeSent);
    console.log("mobileNumber:", mobileNumber);
    console.log("verificationCode:", verificationCode);
    console.log("password:", password);
    console.log("email:", email);

    if (isFormValid) {
      console.log("✅ 表单验证通过，开始登录");
      setIsLoading(true);

      if (isMobileShow) {
        handlePhonePasswordLogin({
          code: verificationCode,
          countryCode: countryCode,
          mobileNumber,
          signinType: signinType,
          captcha: captchaData,
          extra: {},
          clientId: "100033",
          password,
        });
      } else {
        handleEmailSign({
          code: verificationCode,
          email,
          signinType: signinType,
          extra: {},
          clientId: "100033",
          password,
          captcha: {
            captchaID: "",
            captchaOutput: "",
            genTime: "",
            lotNumber: "",
            passToken: "",
          },
        });
      }

      setIsLoading(false);
    } else {
      console.log("❌ 表单验证失败，无法登录");
      console.log("验证失败原因:");
      if (getLoginType === "phoneCode") {
        console.log("- codeSent:", codeSent);
        console.log("- mobileNumber:", mobileNumber.trim() !== "");
        console.log("- verificationCode:", verificationCode.trim() !== "");
      } else if (getLoginType === "phonePassword") {
        console.log("- mobileNumber:", mobileNumber.trim() !== "");
        console.log("- password:", password.trim() !== "");
      }
    }
  }, [
    isFormValid,
    getLoginType,
    codeSent,
    isMobileShow,
    signinType,
    verificationCode,
    countryCode,
    mobileNumber,
    email,
    password,
    captchaData,
  ]);

  mobileNumber.trim();
  // mobileNumber.trim() && verificationCode.trim() && agreedToTerms;
  const canSendCode = useMemo(
    () => (mobileNumber.trim() || email.trim()) && countdown === 0,
    [email, mobileNumber, countdown]
  );

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-3xl shadow-xl max-w-4xl w-full overflow-hidden">
        <div className="flex min-h-[600px]">
          {/* Left side - Registration Form */}
          <div className="flex-1 p-8 max-w-md relative">
            {/* Close button */}
            <div className="flex justify-end mb-6">
              <button className="text-gray-400 hover:text-gray-600 transition-colors">
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Header */}
            <h1 className="text-2xl font-semibold text-gray-900 mb-8">
              Welcome to TanTan
            </h1>

            {/* 邮箱验证码登录 */}
            {getLoginType == "emailCode" && (
              <>
                {/* Email Input */}
                <div className="mb-4">
                  <Input
                    type="email"
                    placeholder="Your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full focus:border-orange-500 focus:ring-orange-500"
                  />
                </div>

                {/* Verification Code */}
                <div className="mb-4">
                  <div className="flex gap-2">
                    <Input
                      disabled={!codeSent}
                      type="text"
                      placeholder="Verification Code"
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value)}
                      className="flex-1 focus:border-orange-500 focus:ring-orange-500"
                      maxLength={6}
                    />
                    <Button
                      onClick={() => handleEmailSendCode()}
                      variant="outline"
                      className={cn(
                        "px-6 min-w-[100px] text-orange-500 border-orange-500 hover:bg-orange-50",
                        (!canSendCode || isLoading) &&
                          "opacity-50 cursor-not-allowed"
                      )}
                      disabled={!canSendCode || isLoading}
                    >
                      {isLoading ? (
                        <div className="w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full animate-spin" />
                      ) : countdown > 0 ? (
                        `${countdown}s`
                      ) : (
                        "Send Code"
                      )}
                    </Button>
                  </div>
                  {/* {codeSent && countdown === 0 && (
                    <p className="text-sm text-green-600 mt-1 flex items-center gap-1">
                      <Check className="w-4 h-4" />
                      Verification code sent successfully
                    </p>
                  )} */}
                </div>
              </>
            )}

            {/* 邮箱密码登录 */}
            {getLoginType == "emailPassword" && (
              <>
                {/* Email Input */}
                <div className="mb-4">
                  <Input
                    type="email"
                    placeholder="Your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full focus:border-orange-500 focus:ring-orange-500"
                  />
                </div>

                {/* Verification Code */}
                <div className="mb-4">
                  <Input
                    type="password"
                    placeholder="Password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="flex-1 focus:border-orange-500 focus:ring-orange-500"
                  />
                </div>
              </>
            )}

            {/* 手机验证码 */}
            {getLoginType === "phoneCode" && (
              <>
                <div className="mb-4 relative">
                  <div className="flex">
                    <CountryCodeSelector
                      value={countryCode}
                      onChange={setCountryCode}
                    />
                    <Input
                      type="tel"
                      placeholder="Your Phone number"
                      value={mobileNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      className="flex-1 rounded-l-none border-l-0 focus:border-orange-500 focus:ring-orange-500 text-gray-500"
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      disabled={!codeSent}
                      placeholder="Verification Code"
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value)}
                      className={
                        (cn(
                          "flex-1 focus:border-orange-500 focus:ring-orange-500"
                        ),
                        codeSent ? "focus:bg-white" : "bg-gray-300 ")
                      }
                      maxLength={6}
                    />
                    <Button
                      onClick={handlePhoneCodeSend}
                      variant="outline"
                      className={cn(
                        "px-6 min-w-[100px] text-orange-500 border-orange-500 hover:bg-orange-50",
                        (!canSendCode || isLoading) &&
                          "opacity-50 cursor-not-allowed"
                      )}
                      disabled={!canSendCode || isLoading}
                    >
                      {isLoading ? (
                        <div className="w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full animate-spin" />
                      ) : countdown > 0 ? (
                        `${countdown}s`
                      ) : (
                        "Send Code"
                      )}
                    </Button>
                  </div>
                </div>
              </>
            )}

            {/* 手机密码登录 */}

            {getLoginType === "phonePassword" && (
              <div className="mb-4 relative">
                <div className="flex">
                  <CountryCodeSelector
                    value={countryCode}
                    onChange={setCountryCode}
                  />
                  <Input
                    type="tel"
                    placeholder="Your Phone number"
                    value={mobileNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="flex-1 rounded-l-none border-l-0 focus:border-orange-500 focus:ring-orange-500 text-gray-500"
                  />
                </div>
              </div>
            )}

            {getLoginType === "phonePassword" && (
              <div className="mb-4">
                <Input
                  type="password"
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="flex-1 focus:border-orange-500 focus:ring-orange-500"
                />
              </div>
            )}

            {/* Password Link */}
            <div className="mb-6">
              <div className="text-sm text-gray-500">
                Verification code issue?{" "}
                {signinType === "confirmation_code" ? (
                  <button
                    onClick={() => {
                      setSigninType("password");
                      setVerificationCode("");
                      setPassword("");
                    }}
                    className="text-orange-500 hover:text-orange-600 underline transition-colors"
                  >
                    Try password
                  </button>
                ) : (
                  <button
                    onClick={() => {
                      setSigninType("confirmation_code");
                      setVerificationCode("");
                      setPassword("");
                    }}
                    className="text-orange-500 hover:text-orange-600 underline transition-colors"
                  >
                    Try verification code
                  </button>
                )}
              </div>
            </div>

            {/* Login Button */}
            <Button
              onClick={handleLogin}
              className={cn(
                "w-full py-3 mb-6 transition-all duration-200",
                isFormValid
                  ? "bg-orange-500 text-white hover:bg-orange-600"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              )}
              disabled={!isFormValid || isLoading}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Logging in...
                </div>
              ) : (
                "Log In"
              )}
            </Button>

            {/* OR Divider */}
            <div className="flex items-center mb-6">
              <div className="flex-1 border-t border-gray-200"></div>
              <span className="px-4 text-sm text-gray-500">OR</span>
              <div className="flex-1 border-t border-gray-200"></div>
            </div>

            {/* Email Login Link */}
            <div className="text-center mb-6">
              {!isMobileShow ? (
                <button
                  onClick={() => setMobileShow(true)}
                  className="text-gray-700 hover:text-gray-900 font-medium transition-colors"
                >
                  Log in with phone
                </button>
              ) : (
                <button
                  onClick={() => setMobileShow(false)}
                  className="text-gray-700 hover:text-gray-900 font-medium transition-colors"
                >
                  Log in with email
                </button>
              )}
            </div>

            {/* Terms Agreement */}
            <div className="flex items-start gap-3">
              {/* <Checkbox
                id="terms"
                checked={agreedToTerms}
                onCheckedChange={(checked) =>
                  setAgreedToTerms(checked as boolean)
                }
                className="mt-1"
              /> */}
              <label
                htmlFor="terms"
                className="text-sm text-gray-600 leading-relaxed cursor-pointer"
              >
                I have read and agree to{" "}
                <a
                  href="https://www.int.tantanapp.com/terms-of-use"
                  target="_blank"
                  onClick={() =>
                    (location.href = "https://www.tantanapp.com/zh-cn/term")
                  }
                  className="text-orange-500 hover:text-orange-600 underline transition-colors"
                >
                  TanTan User Agreement
                </a>{" "}
                &{" "}
                <a
                  href="https://www.int.tantanapp.com/privacy-policy"
                  className="text-orange-500 hover:text-orange-600 underline transition-colors"
                >
                  Privacy Policy
                </a>
              </label>
            </div>
          </div>

          {/* Right side - App Download */}
          <div className="flex-1 bg-white p-8 flex flex-col items-center justify-center">
            {/* Mascot Character */}
            <div className="mb-6">
              <div className="w-20 h-20 bg-gradient-to-b from-orange-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-md relative overflow-hidden">
                <div className="absolute top-[40%] w-full">
                  <div className="flex justify-center gap-6">
                    <div className="w-2 h-2 rounded-full bg-black"></div>
                    <div className="w-2 h-2 rounded-full bg-black"></div>
                  </div>
                  <div className="mt-2 w-3 h-3 bg-black mx-auto rounded-full"></div>
                </div>
              </div>
            </div>

            {/* Download Section */}
            <div className="text-center max-w-xs">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Download the APP
              </h2>
              <p className="text-gray-600 mb-8 leading-relaxed text-sm">
                No TanTan account? Download the app today to create your account
                and unlock full access to all features!
              </p>

              <p className="text-sm text-gray-700 mb-4">
                You can get the app from here:
              </p>

              {/* App Store Buttons */}
              <div className="flex justify-center gap-3">
                <button className="flex items-center justify-center gap-2 bg-black text-white px-3 py-2 rounded-lg hover:bg-gray-800 transition-all duration-200">
                  <div className="w-5 h-5">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
                    </svg>
                  </div>
                  <div className="text-left">
                    <div className="text-[10px] leading-tight">
                      Download on the
                    </div>
                    <div className="text-xs font-semibold leading-tight">
                      App Store
                    </div>
                  </div>
                </button>

                <button className="flex items-center justify-center gap-2 bg-black text-white px-3 py-2 rounded-lg hover:bg-gray-800 transition-all duration-200">
                  <div className="w-5 h-5">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z" />
                    </svg>
                  </div>
                  <div className="text-left">
                    <div className="text-[10px] leading-tight">GET IT ON</div>
                    <div className="text-xs font-semibold leading-tight">
                      Google Play
                    </div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Login Error Dialog */}
        {/* <AlertDialog open={showErrorDialog} >
          <AlertDialogContent
            className="max-w-md w-[90vw] sm:w-full"
            style={{
              position: "fixed",
              left: "50%",
              top: "50%",
              transform: "translate(-50%, -50%)",
              zIndex: 9999,
            }}
          >
            <div className="text-center space-y-3">
              <AlertDialogTitle className="text-lg font-semibold text-gray-900">
                Forgot your password?
              </AlertDialogTitle>
              <AlertDialogDescription className="text-gray-600 leading-relaxed text-sm">
                {errorMessage}
                <br />
                <br />
                You can reset it in the TanTan app under:
                <br />
                <span className="font-medium">
                  Me → Settings → Account & Security → Password Management.
                </span>
              </AlertDialogDescription>
            </div>
            <div className="flex justify-center mt-6 pt-4">
              <AlertDialogAction
                onClick={() => setShowErrorDialog(false)}
                className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-2.5 rounded-lg font-medium min-w-[120px] transition-colors"
              >
                Got it
              </AlertDialogAction>
            </div>
          </AlertDialogContent>
        </AlertDialog> */}
      </div>
    </div>
  );
}
